// stores/inbox.js

import { defineStore } from 'pinia';
import { getInboxEmails } from '@/utils/api';

export const useInboxStore = defineStore('inbox', {
  state: () => ({
    emails: [],
    isLoading: false,
    error: null,
    page: 1,
    pageSize: 20,
    totalEmails: 0,
    hasMore: true,
    selectedAccountId: null,
  }),
  actions: {
   async selectAccount(accountId) {
     this.selectedAccountId = accountId;
     this.emails = [];
     this.page = 1;
     this.totalEmails = 0;
     this.hasMore = true;
     this.error = null;
     await this.fetchEmails();
   },

    async fetchEmails(loadMore = false) {
      if (!this.selectedAccountId) {
        this.error = "Please select an email account.";
        return;
      }
      if (this.isLoading || (!this.hasMore && loadMore)) {
        return;
      }

      this.isLoading = true;
      this.error = null;

      if (!loadMore) {
        this.page = 1;
        this.emails = [];
        this.hasMore = true;
      }

     // stores/inbox.js -> fetchEmails

// ...
      try {
        const response = await getInboxEmails({
          page: this.page,
          pageSize: this.pageSize,
          account_id: this.selectedAccountId
        });

        // ★★★★★ CORE FIX IS HERE ★★★★★
        // Safely access the nested data.
        // The structure could be response.data or response.data.data
        // This handles both cases.
        const responseData = response.data?.data ?? response.data ?? {};
        
        const newEmails = responseData.emails || [];
        const total = responseData.total || 0;
        
        if (newEmails.length > 0) {
          this.emails.push(...newEmails);
        }
        
        this.totalEmails = total;
        this.page += 1;
        
        // Update hasMore status based on the total count
        if (this.emails.length >= this.totalEmails) {
          this.hasMore = false;
        }


      } catch (error) {
        // More robust error handling
        if (error.response && error.response.data && error.response.data.error) {
          this.error = error.response.data.error;
        } else {
          this.error = error.message || 'An unknown error occurred while fetching emails.';
        }
        console.error('Error fetching emails:', error);
      } finally {
        this.isLoading = false;
      }
    },
    getEmailById(messageId) {
      // Corrected to use the right property from backend response
      return this.emails.find(email => email.messageId === messageId);
    }
  },
});